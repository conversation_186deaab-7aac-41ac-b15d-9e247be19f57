inTowTruck = false
currentTowTruck = nil
TowTrucks = {} -- Tabulka pro ukládání stavu jednotlivých odtahovek

-- Funkce pro získání nebo vytvoření stavu odtahovky
function GetTruckState(truck)
    local truckId = NetworkGetNetworkIdFromEntity(truck)
    if not TowTrucks[truckId] then
        TowTrucks[truckId] = {
            bedState = 'raised', -- 'raised', 'lowered', 'moving'
            attachedVehicle = 0
        }
        print('^3[DEBUG - valic_odtahovkar]^7: Nový stav odtahovky vytvořen pro ID: ' .. truckId)
    end
    print('^3[DEBUG - valic_odtahovkar]^7: Získávám stav odtahovky pro ID: ' .. truckId .. ', stav: ' .. TowTrucks[truckId].bedState)
    return TowTrucks[truckId]
end

-- Funkce pro aktualizaci stavu odtahovky
function UpdateTowTruckState(truck, key, value)
    local truckId = NetworkGetNetworkIdFromEntity(truck)
    if TowTrucks[truckId] then
        TowTrucks[truckId][key] = value
        print('^3[DEBUG - valic_odtahovkar]^7: Aktualizuji stav odtahovky ID: ' .. truckId .. ', klíč: ' .. key .. ', hodnota: ' .. tostring(value))
    end
end

-- Funkce pro kontrolu, zda je hráč v odtahovce
CreateThread(function()
    while true do
        Wait(1000)
        local playerPed = PlayerPedId()
        local inVehicle = IsPedInAnyVehicle(playerPed, false)

        if inVehicle then
            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if GetEntityModel(vehicle) == joaat(Config.TowTruckModel) then
                if not inTowTruck then
                    inTowTruck = true
                    currentTowTruck = vehicle
                    local state = GetTruckState(currentTowTruck)
                    exports.valic_odtahovkar:InitTowTruckTargets(currentTowTruck, state)
                    print('^3[DEBUG - valic_odtahovkar]^7: Hráč vstoupil do odtahovky. ID: ' .. NetworkGetNetworkIdFromEntity(currentTowTruck))
                end
            else
                if inTowTruck then
                    inTowTruck = false
                    if currentTowTruck then
                        exports.valic_odtahovkar:RemoveVisualAid()
                        exports.ox_target:removeLocalEntity(currentTowTruck)
                        print('^3[DEBUG - valic_odtahovkar]^7: Hráč opustil odtahovku. ID: ' .. NetworkGetNetworkIdFromEntity(currentTowTruck))
                    end
                    currentTowTruck = nil
                end
            end
        else
            if inTowTruck then
                inTowTruck = false
                if currentTowTruck then
                    exports.valic_odtahovkar:RemoveVisualAid()
                    exports.ox_target:removeLocalEntity(currentTowTruck)
                    print('^3[DEBUG - valic_odtahovkar]^7: Hráč opustil vozidlo (byla odtahovka). ID: ' .. NetworkGetNetworkIdFromEntity(currentTowTruck))
                end
                currentTowTruck = nil
            end
        end
    end
end)

-- Funkce pro zobrazení notifikace
function ShowNotification(key)
    lib.notify({
        title = 'Odtahovka',
        description = Config.Text[key],
        type = 'inform'
    })
    print('^3[DEBUG - valic_odtahovkar]^7: Zobrazuji notifikaci: ' .. Config.Text[key])
end

# <PERSON><PERSON><PERSON> skript představuje pokro<PERSON>ý, ale stále přehled<PERSON>ý systém pro práci odtahovkáře ve FiveM. Je navržen tak, aby byl co nejlehčí a nejjednodušší na úpravu. Využívá moderní nástroje jako `ox_lib` a `ox_target` pro plynulý a vizuálně atraktivní herní z<PERSON>.

## Funkce

- **Plynulá animace korby**: <PERSON><PERSON><PERSON><PERSON> (`flatbed`) se plynule sklápí a zvedá, namísto okamžité změny.
- **Vizuální nápověda**: Po sklopení korby se na zemi objeví značka a 3D text, které hráči přesně ukazují, kam má s vozidlem najet.
- **Animace hráče**: <PERSON><PERSON>i kurtování a odkurtování vozidla postava hráče přehraje animaci, což zvyšuje imerzi.
- **Lokalizace**: Plná podpora více jazyků (CZ/EN) díky integraci s `ox_lib`.
- **Kurtování vozidel**: Možnost bezpečně přikurtovat vozidlo na korbu, aby během přepravy nespadlo.
- **Interaktivní ovládání**: Veškeré akce se provádějí přes `ox_target`.
- **Modulární struktura**: Kód je rozdělen do několika souborů, což usnadňuje orientaci a budoucí úpravy.
- **Snadná konfigurace**: Důležitá nastavení jsou v `config.lua`.

## Závislosti

- **[ox_lib](https://github.com/overextended/ox_lib)**
- **ox_target**

## Instalace

1.  Zkopírujte složku `valic_odtahovkar` do vaší složky `resources`.
2.  Ujistěte se, že máte nainstalované všechny závislosti.
3.  Do `server.cfg` přidejte `ensure valic_odtahovkar`.
4.  Restartujte server.

## Jak používat (Gameplay)

1.  **Nasedněte do odtahovky** (`flatbed`).
2.  **Jděte k zadní části** a přes `ox_target` vyberte **"Sklopit korbu"**.
3.  Korba se plynule sklopí. Za odtahovkou se objeví **žlutá značka na zemi**.
4.  **Najeďte s autem** na tuto značku.
5.  Vraťte se k ovládání a vyberte **"Přikurtovat vozidlo"**. Vaše postava přehraje animaci a vozidlo se připevní.
6.  **Zvedněte korbu** a můžete vyrazit.
7.  Pro vyložení proces opakujte: sklopit korbu, odkurtovat vozidlo a sjet s ním dolů.

## Konfigurace (`config.lua`)

- `Config.TowTruckModel`: Model vozidla pro odtahovku.
- `Config.BedAnimationSpeed`: Rychlost animace korby (menší číslo = pomalejší a plynulejší animace).

## Struktura Skriptu

- **`locales/`**: Složka s překlady (`cs.lua`, `en.lua`).
- **`client/main.lua`**: Hlavní klientský soubor, řídí logiku a stavy.
- **`client/target.lua`**: Definuje `ox_target` interakce.
- **`client/controls.lua`**: Obsahuje výkonné funkce (animace, vizualizace, připojování vozidel).
- **`server/main.lua`**: Připraven pro budoucí serverovou logiku.

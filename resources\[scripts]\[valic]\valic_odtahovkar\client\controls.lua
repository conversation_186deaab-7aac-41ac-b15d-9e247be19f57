local visualAidThread = nil

-- Plynulá animace korby
function AnimateBed(truck, to_state)
    print('^3[DEBUG - valic_odtahovkar]^7: Spouštím AnimateBed pro odtahovku ID: ' .. NetworkGetNetworkIdFromEntity(truck) .. ', cílový stav: ' .. to_state)
    exports.valic_odtahovkar:UpdateTowTruckState(truck, 'bedState', 'moving')
    local doorIndex = 5 -- Index "dveří" pro korbu flatbedu
    local current_ratio = GetVehicleDoorAngleRatio(truck, doorIndex)
    local target_ratio = (to_state == 'lowered') and 1.0 or 0.0
    
    CreateThread(function()
        local start_time = GetGameTimer()
        while GetGameTimer() - start_time < 5000 do -- Max 5 sekund animace
            current_ratio = Lerp(current_ratio, target_ratio, Config.BedAnimationSpeed)
            SetVehicleDoorControl(truck, doorIndex, 5, current_ratio)
            
            if math.abs(current_ratio - target_ratio) < 0.01 then
                break
            end
            Wait(0)
        end
        
        SetVehicleDoorControl(truck, doorIndex, 5, target_ratio) -- Dokončení animace
        exports.valic_odtahovkar:UpdateTowTruckState(truck, 'bedState', to_state)
        exports.valic_odtahovkar:ShowNotification(to_state == 'lowered' and 'lower_bed' or 'raise_bed')
        
        if to_state == 'lowered' then
            exports.valic_odtahovkar:CreateVisualAid(truck)
        else
            exports.valic_odtahovkar:RemoveVisualAid()
        end

        exports.valic_odtahovkar:InitTowTruckTargets(truck, exports.valic_odtahovkar:GetTruckState(truck)) -- Aktualizace targetů
        print('^3[DEBUG - valic_odtahovkar]^7: Animace korby dokončena pro odtahovku ID: ' .. NetworkGetNetworkIdFromEntity(truck) .. ', nový stav: ' .. to_state)
    end)
end

function LowerBed(truck)
    print('^3[DEBUG - valic_odtahovkar]^7: Volám LowerBed pro odtahovku ID: ' .. NetworkGetNetworkIdFromEntity(truck))
    AnimateBed(truck, 'lowered')
end

function RaiseBed(truck)
    print('^3[DEBUG - valic_odtahovkar]^7: Volám RaiseBed pro odtahovku ID: ' .. NetworkGetNetworkIdFromEntity(truck))
    AnimateBed(truck, 'raised')
end

-- Vizuální nápověda pro naložení
function CreateVisualAid(truck)
    print('^3[DEBUG - valic_odtahovkar]^7: Vytvářím vizuální nápovědu pro odtahovku ID: ' .. NetworkGetNetworkIdFromEntity(truck))
    exports.valic_odtahovkar:RemoveVisualAid() -- Jistota, že neexistuje starý thread
    
    visualAidThread = CreateThread(function()
        local offset = GetOffsetFromEntityInWorldCoords(truck, 0.0, -7.0, 0.5)
        while exports.valic_odtahovkar:GetTruckState(truck).bedState == 'lowered' do
            DrawMarker(2, offset.x, offset.y, offset.z - 0.9, 0, 0, 0, 0, 0, 0, 3.0, 3.0, 1.0, 255, 200, 0, 100, false, true, 2, false, false, false, false)
            lib.showTextUI(Config.Text.attach_point_text, {
                position = {
                    x = offset.x,
                    y = offset.y,
                    z = offset.z,
                },
            })
            Wait(0)
        end
        print('^3[DEBUG - valic_odtahovkar]^7: Vizuální nápověda ukončena pro odtahovku ID: ' .. NetworkGetNetworkIdFromEntity(truck))
    end)
end

function RemoveVisualAid()
    if visualAidThread then
        lib.hideTextUI()
        -- Thread se zastaví sám, protože podmínka `while bedState == 'lowered'` již nebude platit.
        -- Použití killThread není bezpečné.
        visualAidThread = nil
        print('^3[DEBUG - valic_odtahovkar]^7: Vizuální nápověda odstraněna.')
    end
end

-- Animace hráče
function PlayAttachAnim()
    print('^3[DEBUG - valic_odtahovkar]^7: Spouštím animaci přikurtování.')
    local playerPed = PlayerPedId()
    local dict = "anim@amb@clubhouse@tutorial@biker_tutorial_clubhouse@"
    lib.requestAnimDict(dict)
    TaskPlayAnim(playerPed, dict, "machinist_loop_mechandplayer", 8.0, -8.0, 2000, 49, 0, false, false, false)
    Wait(2000)
    ClearPedTasks(playerPed)
    print('^3[DEBUG - valic_odtahovkar]^7: Animace přikurtování dokončena.')
end

-- Funkce pro přikurtování a odkurtování
function AttachVehicleToBed(truck)
    print('^3[DEBUG - valic_odtahovkar]^7: Volám AttachVehicleToBed pro odtahovku ID: ' .. NetworkGetNetworkIdFromEntity(truck))
    local attachCoords = GetOffsetFromEntityInWorldCoords(truck, 0.0, -7.0, 0.5)
    local closestVehicle = GetClosestVehicle(attachCoords, 5.0, 0, 70)

    if closestVehicle == 0 or closestVehicle == truck then
        exports.valic_odtahovkar:ShowNotification('no_vehicle_nearby')
        print('^3[DEBUG - valic_odtahovkar]^7: V blízkosti není žádné vozidlo k naložení.')
        return
    end
    
    PlayAttachAnim()
    AttachVehicleToTowTruck(truck, closestVehicle, -1, 0.0, -5.0, 1.0)
    exports.valic_odtahovkar:UpdateTowTruckState(truck, 'attachedVehicle', closestVehicle)
    exports.valic_odtahovkar:ShowNotification('vehicle_attached')
    exports.valic_odtahovkar:InitTowTruckTargets(truck, exports.valic_odtahovkar:GetTruckState(truck))
    print('^3[DEBUG - valic_odtahovkar]^7: Vozidlo ID: ' .. NetworkGetNetworkIdFromEntity(closestVehicle) .. ' přikurtováno k odtahovce ID: ' .. NetworkGetNetworkIdFromEntity(truck))
end

function DetachVehicleFromBed(truck)
    print('^3[DEBUG - valic_odtahovkar]^7: Volám DetachVehicleFromBed pro odtahovku ID: ' .. NetworkGetNetworkIdFromEntity(truck))
    local attachedVehicle = GetVehicleAttachedToTowTruck(truck)
    if attachedVehicle ~= 0 then
        PlayAttachAnim()
        DetachVehicleFromTowTruck(truck, attachedVehicle)
        
        local vehicle_coords = GetEntityCoords(attachedVehicle)
        SetEntityCoords(attachedVehicle, vehicle_coords.x, vehicle_coords.y - 2.0, vehicle_coords.z, false, false, false, true)
        
        exports.valic_odtahovkar:UpdateTowTruckState(truck, 'attachedVehicle', 0)
        exports.valic_odtahovkar:ShowNotification('vehicle_detached')
        exports.valic_odtahovkar:InitTowTruckTargets(truck, exports.valic_odtahovkar:GetTruckState(truck))
        print('^3[DEBUG - valic_odtahovkar]^7: Vozidlo ID: ' .. NetworkGetNetworkIdFromEntity(attachedVehicle) .. ' odkurtováno z odtahovky ID: ' .. NetworkGetNetworkIdFromEntity(truck))
    else
        print('^3[DEBUG - valic_odtahovkar]^7: Na odtahovce ID: ' .. NetworkGetNetworkIdFromEntity(truck) .. ' není žádné vozidlo k odkurtování.')
    end
end

exports('LowerBed', LowerBed)
exports('RaiseBed', RaiseBed)
exports('AttachVehicleToBed', AttachVehicleToBed)
exports('DetachVehicleFromBed', DetachVehicleFromBed)
exports('CreateVisualAid', CreateVisualAid)
exports('RemoveVisualAid', RemoveVisualAid)


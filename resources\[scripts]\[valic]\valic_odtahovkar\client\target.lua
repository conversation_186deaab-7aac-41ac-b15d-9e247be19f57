function InitTowTruckTargets(truck, state)
    print('^3[DEBUG - valic_odtahovkar]^7: Inicializuji ox_target pro odtahovku ID: ' .. NetworkGetNetworkIdFromEntity(truck) .. ', stav korby: ' .. state.bedState .. ', př<PERSON><PERSON><PERSON><PERSON><PERSON>: ' .. tostring(state.attachedVehicle))
    exports.ox_target:removeLocalEntity(truck) -- Nejdříve odstraníme staré, abychom předešli duplikaci
    exports.ox_target:addLocalEntity(truck, {
        {
            name = 'towtruck_bed_controls',
            icon = 'fa-solid fa-gears',
            label = (state.bedState == 'raised') and Config.Text.lower_bed or Config.Text.raise_bed,
            onSelect = function()
                if state.bedState == 'moving' then
                    exports.valic_odtahovkar:ShowNotification('bed_is_moving')
                    return
                end

                if state.bedState == 'raised' then
                    exports.valic_odtahovkar:LowerBed(truck)
                else
                    exports.valic_odtahovkar:RaiseBed(truck)
                end
            end
        },
        {
            name = 'towtruck_attach_vehicle',
            icon = 'fa-solid fa-link',
            label = Config.Text.attach_vehicle,
            onSelect = function()
                exports.valic_odtahovkar:AttachVehicleToBed(truck)
            end,
            canInteract = function()
                return state.bedState == 'lowered' and state.attachedVehicle == 0
            end
        },
        {
            name = 'towtruck_detach_vehicle',
            icon = 'fa-solid fa-unlink',
            label = Config.Text.detach_vehicle,
            onSelect = function()
                exports.valic_odtahovkar:DetachVehicleFromBed(truck)
            end,
            canInteract = function()
                return state.attachedVehicle ~= 0
            end
        }
    })
end

exports('InitTowTruckTargets', InitTowTruckTargets)


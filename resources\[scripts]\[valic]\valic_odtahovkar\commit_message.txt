fix(odtahovkar): Refactor client-side state management and exports

This commit addresses several issues related to client-side state management and function accessibility:
- Centralized tow truck state in `client/main.lua` using a `TowTrucks` table.
- Modified `InitTowTruckTargets` in `client/target.lua` to accept and use the `state` argument instead of global variables.
- Updated `client/controls.lua` to use exported functions for state updates, notifications, and target initialization.
- Exported necessary functions (`GetTruckState`, `UpdateTowTruckState`, `ShowNotification`, `InitTowTruckTargets`, `RemoveVisualAid`, `LowerBed`, `RaiseBed`, `AttachVehicleToBed`, `DetachVehicleFromBed`, `CreateVisualAid`) via `fxmanifest.lua` and within their respective client scripts to ensure proper inter-script communication.
- Removed redundant global variables in `client/target.lua` and `client/controls.lua`.